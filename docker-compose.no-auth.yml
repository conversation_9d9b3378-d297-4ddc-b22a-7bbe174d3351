# =============================================================================
# Docker Compose Override for No-Auth Mode
# Use with: docker-compose -f docker-compose.yml -f docker-compose.no-auth.yml up
# =============================================================================

version: '3.8'

services:
  # -----------------------------------------------------------------------------
  # Override main service for no-auth mode
  # -----------------------------------------------------------------------------
  webscout-api:
    environment:
      # Enable no-auth mode for development/demos
      - WEBSCOUT_NO_AUTH=true
      - WEBSCOUT_NO_RATE_LIMIT=true
      - WEBSCOUT_LOG_LEVEL=debug
      - WEBSCOUT_DEBUG=true
      # FastAPI metadata (new)
      - WEBSCOUT_API_TITLE=Webscout OpenAI API
      - WEBSCOUT_API_DESCRIPTION=OpenAI API compatible interface for various LLM providers with enhanced authentication
      - WEBSCOUT_API_VERSION=0.2.0
      - WEBSCOUT_API_DOCS_URL=/docs
      - WEBSCOUT_API_REDOC_URL=/redoc
      - WEBSCOUT_API_OPENAPI_URL=/openapi.json

    # Add labels for easy identification
    labels:
      - "webscout.mode=no-auth"
      - "webscout.description=No authentication required - development/demo mode"

  # -----------------------------------------------------------------------------
  # Override production service for no-auth mode (not recommended)
  # -----------------------------------------------------------------------------
  webscout-api-production:
    environment:
      # WARNING: Only use no-auth in production if you understand the security implications
      - WEBSCOUT_NO_AUTH=true
      - WEBSCOUT_NO_RATE_LIMIT=false  # Keep rate limiting in production
      - WEBSCOUT_LOG_LEVEL=info

    labels:
      - "webscout.mode=no-auth-production"
      - "webscout.warning=No authentication - use with caution in production"

  # -----------------------------------------------------------------------------
  # Override development service for no-auth mode
  # -----------------------------------------------------------------------------
  webscout-api-dev:
    environment:
      # Perfect for development
      - WEBSCOUT_NO_AUTH=true
      - WEBSCOUT_NO_RATE_LIMIT=true
      - WEBSCOUT_LOG_LEVEL=debug
      - WEBSCOUT_DEBUG=true

    labels:
      - "webscout.mode=no-auth-dev"
      - "webscout.description=Development mode with no authentication"
