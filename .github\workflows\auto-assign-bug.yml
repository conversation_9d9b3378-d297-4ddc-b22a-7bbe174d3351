name: Auto-assign Copilot for Bug Reports

on:
  issues:
    types: [labeled]

permissions:
  issues: write
  contents: read

jobs:
  auto-assign:
    runs-on: ubuntu-latest
    if: github.event.label.name == 'bug'
    steps:
      - name: Assign Copilot to bug report
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const issue = context.payload.issue;
            const assignee = 'copilot';
            
            // Check if the issue already has assignees
            if (issue.assignees && issue.assignees.length > 0) {
              console.log('Issue already has assignees, skipping auto-assignment');
              return;
            }
            
            try {
              // Try to assign the copilot
              await github.rest.issues.addAssignees({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue.number,
                assignees: [assignee]
              });
              
              console.log(`Successfully assigned ${assignee} to issue #${issue.number}`);
              
              // Add a comment to notify about the assignment
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue.number,
                body: `🐛 This bug report has been automatically assigned to @${assignee} for review and resolution.`
              });
              
            } catch (error) {
              console.log(`Could not assign ${assignee}: ${error.message}`);
              
              // Fallback: Add a comment mentioning copilot instead
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issue.number,
                body: `🐛 This bug report has been labeled as a bug. @${assignee} please review this issue when possible.`
              });
            }